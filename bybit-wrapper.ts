import { RestClientV5 } from "bybit-api";
import { bybit } from "./env.ts";

export interface RateLimitInfo {
  remainingRequests: number;
  maxRequests: number;
  resetAtTimestamp: number;
}

export interface RequestLog {
  timestamp: number;
  method: string;
  params: any;
  success: boolean;
  response?: any;
  error?: any;
  duration: number;
  rateLimitInfo?: RateLimitInfo;
}

export class BybitWrapper {
  private client: RestClientV5;
  private lastRateLimit: RateLimitInfo | null = null;
  private requestLogs: RequestLog[] = [];

  constructor() {
    this.client = new RestClientV5({
      key: bybit.apiKey,
      secret: bybit.apiSecret,
      testnet: bybit.demo,
      demoTrading: bybit.demo,
      parseAPIRateLimits: true,
    });
  }

  private async handleRateLimit(): Promise<void> {
    if (this.lastRateLimit) {
      const now = Date.now();
      
      // If we're close to the limit, wait until reset
      if (this.lastRateLimit.remainingRequests <= 2) {
        const waitTime = this.lastRateLimit.resetAtTimestamp - now;
        if (waitTime > 0 && waitTime < 60000) { // Max 1 minute wait
          console.log(`⏳ Rate limit reached, waiting ${waitTime}ms...`);
          await new Promise(resolve => setTimeout(resolve, waitTime + 100));
        }
      }
    }
  }

  private updateRateLimit(response: any): void {
    if (response?.rateLimitApi) {
      this.lastRateLimit = response.rateLimitApi;
    }
  }

  async getInstruments(): Promise<any[]> {
    await this.handleRateLimit();
    const response = await this.client.getInstrumentsInfo({
      category: 'linear',
      limit: 1000
    });
    this.updateRateLimit(response);
    
    return response.result?.list?.filter(instrument => 
      instrument.status === 'Trading' && 
      instrument.quoteCoin === 'USDT'
    ) || [];
  }

  async getKlines(symbol: string, interval: string, limit: number = 1000, end?: number): Promise<any[]> {
    await this.handleRateLimit();
    const response = await this.client.getKline({
      category: 'linear',
      symbol,
      interval: interval as any,
      limit,
      end
    });
    this.updateRateLimit(response);
    
    return response.result?.list || [];
  }

  async getTickers(): Promise<any[]> {
    await this.handleRateLimit();
    const response = await this.client.getTickers({
      category: 'linear'
    });
    this.updateRateLimit(response);
    
    return response.result?.list || [];
  }

  async getWalletBalance(): Promise<any> {
    await this.handleRateLimit();
    const response = await this.client.getWalletBalance({
      accountType: 'UNIFIED'
    });
    this.updateRateLimit(response);
    
    return response.result?.list?.[0];
  }

  async getPositions(): Promise<any[]> {
    await this.handleRateLimit();
    const response = await this.client.getPositionInfo({
      category: 'linear'
    });
    this.updateRateLimit(response);
    
    return response.result?.list || [];
  }

  async setLeverage(symbol: string, leverage: number): Promise<void> {
    await this.handleRateLimit();
    const response = await this.client.setLeverage({
      category: 'linear',
      symbol,
      buyLeverage: leverage.toString(),
      sellLeverage: leverage.toString()
    });
    this.updateRateLimit(response);
  }

  async submitOrder(params: {
    symbol: string;
    side: 'Buy' | 'Sell';
    orderType: 'Market';
    qty: string;
    reduceOnly?: boolean;
    timeInForce?: 'IOC';
  }): Promise<any> {
    await this.handleRateLimit();
    const response = await this.client.submitOrder({
      category: 'linear',
      ...params
    });
    this.updateRateLimit(response);
    
    return response.result;
  }

  getRateLimitInfo(): RateLimitInfo | null {
    return this.lastRateLimit;
  }
}
