[{"timestamp": *************, "method": "getWalletBalance", "params": {"accountType": "UNIFIED"}, "success": true, "duration": 790, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 1, "hasResult": true, "orderId": null, "listLength": 1}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": *************, "method": "getPositionInfo", "params": {"category": "linear"}, "success": true, "duration": 624, "response": {"retCode": 10001, "retMsg": "Missing some parameters that must be filled in, symbol or settleCoin", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": *************, "method": "getWalletBalance", "params": {"accountType": "UNIFIED"}, "success": true, "duration": 241, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 1, "hasResult": true, "orderId": null, "listLength": 1}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": *************, "method": "getPositionInfo", "params": {"category": "linear"}, "success": true, "duration": 600, "response": {"retCode": 10001, "retMsg": "Missing some parameters that must be filled in, symbol or settleCoin", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": *************, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 1016, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}}, {"timestamp": *************, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 1390, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}}, {"timestamp": 1749830436091, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 1383, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": 1749830437475, "method": "setLeverage", "params": {"category": "linear", "symbol": "OBTUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 244, "response": {"retCode": 110043, "retMsg": "leverage not modified", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830437617}}, {"timestamp": 1749830437720, "method": "submitOrder", "params": {"category": "linear", "symbol": "OBTUSDT", "side": "Buy", "orderType": "Market", "qty": "15114.600", "timeInForce": "IOC"}, "success": true, "duration": 616, "response": {"retCode": 10001, "retMsg": "Qty invalid", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830438235}}, {"timestamp": 1749830439339, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 262, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830438235}}, {"timestamp": 1749830439602, "method": "setLeverage", "params": {"category": "linear", "symbol": "ZEREBROUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 279, "response": {"retCode": 110043, "retMsg": "leverage not modified", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830439778}}, {"timestamp": 1749830439881, "method": "submitOrder", "params": {"category": "linear", "symbol": "ZEREBROUSDT", "side": "<PERSON>ll", "orderType": "Market", "qty": "4818.137", "timeInForce": "IOC"}, "success": true, "duration": 1332, "response": {"retCode": 10001, "retMsg": "Qty invalid", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830441111}}, {"timestamp": 1749830442216, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 1005, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830441111}}, {"timestamp": 1749830443221, "method": "setLeverage", "params": {"category": "linear", "symbol": "ANIMEUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 233, "response": {"retCode": 110043, "retMsg": "leverage not modified", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830443352}}, {"timestamp": 1749830443454, "method": "submitOrder", "params": {"category": "linear", "symbol": "ANIMEUSDT", "side": "Buy", "orderType": "Market", "qty": "3961.756", "timeInForce": "IOC"}, "success": true, "duration": 239, "response": {"retCode": 10001, "retMsg": "Qty invalid", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830443585}}, {"timestamp": 1749830444694, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 447, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830443585}}, {"timestamp": 1749830445142, "method": "setLeverage", "params": {"category": "linear", "symbol": "ZBCNUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 254, "response": {"retCode": 110043, "retMsg": "leverage not modified", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830445294}}, {"timestamp": 1749830445397, "method": "submitOrder", "params": {"category": "linear", "symbol": "ZBCNUSDT", "side": "Buy", "orderType": "Market", "qty": "20941.437", "timeInForce": "IOC"}, "success": true, "duration": 235, "response": {"retCode": 10001, "retMsg": "Qty invalid", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830445530}}, {"timestamp": 1749830446638, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 351, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830445530}}, {"timestamp": 1749830446989, "method": "setLeverage", "params": {"category": "linear", "symbol": "RVNUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 264, "response": {"retCode": 110043, "retMsg": "leverage not modified", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830447143}}, {"timestamp": 1749830447253, "method": "submitOrder", "params": {"category": "linear", "symbol": "RVNUSDT", "side": "Buy", "orderType": "Market", "qty": "2788.049", "timeInForce": "IOC"}, "success": true, "duration": 299, "response": {"retCode": 10001, "retMsg": "Qty invalid", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830447450}}, {"timestamp": 1749830448556, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 446, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830447450}}, {"timestamp": 1749830449003, "method": "setLeverage", "params": {"category": "linear", "symbol": "HMSTRUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 231, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830449130}}, {"timestamp": 1749830449236, "method": "submitOrder", "params": {"category": "linear", "symbol": "HMSTRUSDT", "side": "<PERSON>ll", "orderType": "Market", "qty": "76738.425", "timeInForce": "IOC"}, "success": true, "duration": 231, "response": {"retCode": 10001, "retMsg": "Qty invalid", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830449365}}, {"timestamp": 1749830450473, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 445, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830449365}}, {"timestamp": 1749830450920, "method": "setLeverage", "params": {"category": "linear", "symbol": "ORBSUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 237, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830451054}}, {"timestamp": 1749830451157, "method": "submitOrder", "params": {"category": "linear", "symbol": "ORBSUSDT", "side": "<PERSON>ll", "orderType": "Market", "qty": "2854.635", "timeInForce": "IOC"}, "success": true, "duration": 236, "response": {"retCode": 10001, "retMsg": "Qty invalid", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830451290}}, {"timestamp": 1749830452397, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 1376, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830451290}}, {"timestamp": 1749830453774, "method": "setLeverage", "params": {"category": "linear", "symbol": "SPELLUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 231, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830453902}}, {"timestamp": 1749830454005, "method": "submitOrder", "params": {"category": "linear", "symbol": "SPELLUSDT", "side": "<PERSON>ll", "orderType": "Market", "qty": "139911.586", "timeInForce": "IOC"}, "success": true, "duration": 231, "response": {"retCode": 10001, "retMsg": "Qty invalid", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830454134}}, {"timestamp": 1749830455240, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 261, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830454134}}, {"timestamp": 1749830455502, "method": "setLeverage", "params": {"category": "linear", "symbol": "MYRIAUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 232, "response": {"retCode": 110043, "retMsg": "leverage not modified", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830455631}}, {"timestamp": 1749830455734, "method": "submitOrder", "params": {"category": "linear", "symbol": "MYRIAUSDT", "side": "Buy", "orderType": "Market", "qty": "33557.788", "timeInForce": "IOC"}, "success": true, "duration": 232, "response": {"retCode": 10001, "retMsg": "Qty invalid", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830455865}}, {"timestamp": 1749830457010, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 268, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830455865}}, {"timestamp": 1749830457279, "method": "setLeverage", "params": {"category": "linear", "symbol": "SHIB1000USDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 234, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830457411}}, {"timestamp": 1749*********, "method": "submitOrder", "params": {"category": "linear", "symbol": "SHIB1000USDT", "side": "<PERSON>ll", "orderType": "Market", "qty": "5175.349", "timeInForce": "IOC"}, "success": true, "duration": 236, "response": {"retCode": 10001, "retMsg": "Qty invalid", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": *************}}, {"timestamp": *************, "method": "getWalletBalance", "params": {"accountType": "UNIFIED"}, "success": true, "duration": 243, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 1, "hasResult": true, "orderId": null, "listLength": 1}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": *************, "method": "getPositionInfo", "params": {"category": "linear"}, "success": true, "duration": 234, "response": {"retCode": 10001, "retMsg": "Missing some parameters that must be filled in, symbol or settleCoin", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}]